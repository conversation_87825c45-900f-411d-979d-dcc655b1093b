#include <bits/stdc++.h>
using namespace std;
int n;
bool check(int x){
    for (int i=2;i*i<=x;i++){
        if (x%i == 0) return 0;
    }
    return 1;
}
int main(){
    unordered_map<int,int> mp;
    vector<int> f(49);
    f[1] = 1;f[2] = 1;
    for (int i=3;i<=48;i++){
        f[i] = f[i-1]+f[i-2];
    }
    cin >> n;
    int ans = f[n];
    cout << ans << '=';
    for (int i=2;i*i<=ans;i++){
        if (check(i)){
            while(n%i==0){
                mp[i]++;
                ans/=i;
            }
        }
    }
    if (ans != 1) mp[ans]++;
    int cnt = 0;
    for (const auto& i : mp){
        if (i.second == 1){
            cout << i.first;
        }else{
            for (int j=1;j<=i.second;j++){
                cout << i.first;
                if (j < i.second) cout << '*';
            }
        }
        cnt++;
        if (cnt < mp.size()) cout << '*';
    }
    return 0;
}